/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #ffffff;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header Styles */
.header {
    background: linear-gradient(135deg, #1877f2 0%, #42a5f5 100%);
    color: white;
    padding: 1rem 0;
    position: fixed;
    top: 0;
    width: 100%;
    z-index: 1000;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.logo {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

.nav-menu {
    display: flex;
    list-style: none;
    gap: 2rem;
}

.nav-menu a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.nav-menu a:hover {
    opacity: 0.8;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 120px 0 80px;
    margin-top: 70px;
}

.hero-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.hero-content {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 4rem;
    align-items: center;
}

.hero-image img {
    width: 100%;
    max-width: 400px;
    border-radius: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.hero-text h1 {
    font-size: 3rem;
    font-weight: 700;
    color: #1877f2;
    margin-bottom: 1rem;
}

.hero-subtitle {
    font-size: 1.25rem;
    color: #666;
    margin-bottom: 1.5rem;
    font-weight: 500;
}

.hero-description {
    font-size: 1.1rem;
    color: #555;
    margin-bottom: 2rem;
    line-height: 1.7;
}

.hero-stats {
    display: flex;
    gap: 2rem;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1877f2;
}

.stat-label {
    display: block;
    font-size: 0.9rem;
    color: #666;
    margin-top: 0.25rem;
}

/* About Section */
.about {
    padding: 80px 0;
    background: white;
}

.about h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #1877f2;
    margin-bottom: 3rem;
    font-weight: 700;
}

.about-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 2rem;
}

.about-card {
    background: #f8f9fa;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.about-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.about-card h3 {
    font-size: 1.25rem;
    color: #1877f2;
    margin-bottom: 1rem;
    font-weight: 600;
}

.about-card p {
    color: #666;
    line-height: 1.6;
}

/* Biography Section */
.biography {
    padding: 80px 0;
    background: #f8f9fa;
}

.biography h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #1877f2;
    margin-bottom: 3rem;
    font-weight: 700;
}

.bio-section {
    margin-bottom: 3rem;
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.bio-section h3 {
    font-size: 1.5rem;
    color: #1877f2;
    margin-bottom: 1.5rem;
    font-weight: 600;
}

.bio-section p {
    color: #555;
    line-height: 1.8;
    margin-bottom: 1rem;
    font-size: 1.05rem;
}

/* Timeline Section */
.timeline {
    padding: 80px 0;
    background: white;
}

.timeline h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #1877f2;
    margin-bottom: 3rem;
    font-weight: 700;
}

.timeline-container {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.timeline-container::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 3px;
    background: linear-gradient(to bottom, #1877f2, #42a5f5);
    transform: translateX(-50%);
}

.timeline-item {
    display: flex;
    align-items: center;
    margin-bottom: 3rem;
    position: relative;
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-year {
    background: #1877f2;
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    font-size: 1.1rem;
    min-width: 80px;
    text-align: center;
    position: relative;
    z-index: 2;
}

.timeline-content {
    background: white;
    padding: 1.5rem 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin: 0 2rem;
    flex: 1;
    max-width: 300px;
}

.timeline-content h3 {
    color: #1877f2;
    margin-bottom: 0.5rem;
    font-weight: 600;
}

.timeline-content p {
    color: #666;
    line-height: 1.6;
}

/* Achievements Section */
.achievements {
    padding: 80px 0;
    background: #f8f9fa;
}

.achievements h2 {
    text-align: center;
    font-size: 2.5rem;
    color: #1877f2;
    margin-bottom: 3rem;
    font-weight: 700;
}

.achievements-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.achievement-card {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.achievement-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.achievement-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
}

.achievement-card h3 {
    font-size: 1.25rem;
    color: #1877f2;
    margin-bottom: 1rem;
    font-weight: 600;
}

.achievement-card p {
    color: #666;
    line-height: 1.6;
}

/* Footer */
.footer {
    background: #1877f2;
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.footer-links {
    display: flex;
    gap: 2rem;
}

.footer-links a {
    color: white;
    text-decoration: none;
    font-weight: 500;
    transition: opacity 0.3s ease;
}

.footer-links a:hover {
    opacity: 0.8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    .hero-content {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 2rem;
    }

    .hero-text h1 {
        font-size: 2rem;
    }

    .hero-stats {
        justify-content: center;
        gap: 1rem;
    }

    .about-grid {
        grid-template-columns: 1fr;
    }

    .timeline-container::before {
        left: 30px;
    }

    .timeline-item {
        flex-direction: row !important;
        padding-left: 60px;
    }

    .timeline-item:nth-child(even) {
        flex-direction: row !important;
    }

    .timeline-year {
        position: absolute;
        left: 0;
        min-width: 60px;
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }

    .timeline-content {
        margin: 0;
        max-width: none;
    }

    .achievements-grid {
        grid-template-columns: 1fr;
    }

    .footer-content {
        flex-direction: column;
        text-align: center;
    }

    .bio-section {
        padding: 1.5rem;
    }

    .container {
        padding: 0 15px;
    }
}

@media (max-width: 480px) {
    .hero {
        padding: 100px 0 60px;
    }

    .hero-text h1 {
        font-size: 1.75rem;
    }

    .hero-subtitle {
        font-size: 1rem;
    }

    .about h2,
    .biography h2,
    .timeline h2,
    .achievements h2 {
        font-size: 2rem;
    }

    .about,
    .biography,
    .timeline,
    .achievements {
        padding: 60px 0;
    }

    .about-card,
    .achievement-card {
        padding: 1.5rem;
    }

    .bio-section {
        padding: 1rem;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Additional hover effects */
.nav-menu a,
.footer-links a {
    position: relative;
}

.nav-menu a::after,
.footer-links a::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: white;
    transition: width 0.3s ease;
}

.nav-menu a:hover::after,
.footer-links a:hover::after {
    width: 100%;
}